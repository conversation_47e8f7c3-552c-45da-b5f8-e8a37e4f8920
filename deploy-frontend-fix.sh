#!/bin/bash

# Deploy Frontend Fix for Horoscope Loading Issue
# This script rebuilds and deploys the frontend with correct API configuration

set -e

# Configuration
SERVER_IP="************"
SERVER_USER="ubuntu"
SSH_KEY="/tmp/kubera_deploy.pem"
REMOTE_PATH="/var/www/kubera.help"
DOMAIN="kubera.help"

echo "🚀 Deploying Frontend Fix for Horoscope Loading..."
echo "=================================================="

# Ensure SSH key exists and has correct permissions
if [ ! -f "$SSH_KEY" ]; then
    echo "❌ SSH key not found at $SSH_KEY"
    echo "Copying SSH key from kubera.pem..."
    cp kubera.pem "$SSH_KEY"
    chmod 600 "$SSH_KEY"
fi

# Test SSH connection
echo "🔍 Testing SSH connection..."
ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$SERVER_USER@$SERVER_IP" "echo 'SSH connection successful'"

# Step 1: Configure environment variables for production build
echo "⚙️ Configuring environment variables..."
cat > .env << EOF
REACT_APP_API_URL=https://$DOMAIN/api
REACT_APP_DOMAIN=$DOMAIN
NODE_ENV=production
EOF

echo "✅ Environment variables configured:"
cat .env

# Step 2: Clean and rebuild frontend
echo "🏗️ Building frontend for production..."
echo "Cleaning previous build..."
rm -rf build/

echo "Installing dependencies..."
npm install

echo "Building React app..."
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Frontend build failed!"
    exit 1
fi

echo "✅ Frontend build completed successfully"

# Step 3: Create backup of current production build
echo "💾 Creating backup of current production build..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "cd $REMOTE_PATH && sudo cp -r build build_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true"

# Step 4: Upload new build
echo "📤 Uploading new frontend build..."
echo "Compressing build directory..."
tar -czf build.tar.gz build/

echo "Uploading compressed build..."
scp -i "$SSH_KEY" build.tar.gz "$SERVER_USER@$SERVER_IP:$REMOTE_PATH/"

echo "Extracting build on server..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "cd $REMOTE_PATH && tar -xzf build.tar.gz && sudo chown -R www-data:www-data build/ && rm build.tar.gz"

# Step 5: Test the deployment
echo "🧪 Testing deployment..."

echo "Testing static file access..."
STATIC_TEST=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/)
echo "Static file response code: $STATIC_TEST"

echo "Testing API health..."
API_HEALTH=$(curl -s https://$DOMAIN/api/health)
echo "API health response: $API_HEALTH"

echo "Testing horoscope API..."
HOROSCOPE_TEST=$(curl -s https://$DOMAIN/api/horoscope/taurus | head -100)
echo "Horoscope API test (first 100 chars): $HOROSCOPE_TEST"

# Step 6: Check Nginx configuration
echo "🔧 Checking Nginx configuration..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "sudo nginx -t"

# Step 7: Reload Nginx to ensure proper serving
echo "🔄 Reloading Nginx..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "sudo systemctl reload nginx"

# Clean up local files
echo "🧹 Cleaning up..."
rm -f build.tar.gz

echo ""
echo "🎉 Frontend deployment completed!"
echo "✅ New build deployed with correct API configuration"
echo "🔗 Website URL: https://$DOMAIN"
echo ""
echo "📊 Next steps:"
echo "1. Open https://$DOMAIN in your browser"
echo "2. Check browser console for any remaining errors"
echo "3. Test horoscope loading for different zodiac signs"
echo "4. Test order placement functionality"
echo ""
echo "🔍 If issues persist, check:"
echo "- Browser console errors"
echo "- Network tab in developer tools"
echo "- PM2 logs: pm2 logs kubera-backend"
echo "- Nginx logs: sudo tail -f /var/log/nginx/error.log"
