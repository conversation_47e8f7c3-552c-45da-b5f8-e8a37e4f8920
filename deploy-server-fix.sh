#!/bin/bash

# Deploy Server Fix for Order Processing
# This script updates the server.js file on production to include order processing functionality

set -e

# Configuration
SERVER_IP="************"
SERVER_USER="ubuntu"
SSH_KEY="/tmp/kubera_deploy.pem"
REMOTE_PATH="/var/www/kubera.help/server"
LOCAL_SERVER_PATH="./server"

echo "🚀 Deploying Server Fix for Order Processing..."
echo "================================================"

# Ensure SSH key exists and has correct permissions
if [ ! -f "$SSH_KEY" ]; then
    echo "❌ SSH key not found at $SSH_KEY"
    echo "Copying SSH key from kubera.pem..."
    cp kubera.pem "$SSH_KEY"
    chmod 600 "$SSH_KEY"
fi

# Test SSH connection
echo "🔍 Testing SSH connection..."
ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$SERVER_USER@$SERVER_IP" "echo 'SSH connection successful'"

# Create backup of current server.js
echo "💾 Creating backup of current server.js..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "cd $REMOTE_PATH && cp server.js server.js.backup.$(date +%Y%m%d_%H%M%S)"

# Upload new server.js
echo "📤 Uploading updated server.js..."
scp -i "$SSH_KEY" "$LOCAL_SERVER_PATH/server.js" "$SERVER_USER@$SERVER_IP:$REMOTE_PATH/"

# Upload package.json if it has changes
echo "📤 Uploading package.json..."
scp -i "$SSH_KEY" "$LOCAL_SERVER_PATH/package.json" "$SERVER_USER@$SERVER_IP:$REMOTE_PATH/"

# Install any new dependencies
echo "📦 Installing dependencies..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "cd $REMOTE_PATH && npm install"

# Restart the server with PM2
echo "🔄 Restarting server..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "pm2 restart kubera-backend"

# Wait a moment for restart
sleep 3

# Check server status
echo "✅ Checking server status..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "pm2 status kubera-backend"

# Test API endpoints
echo "🧪 Testing API endpoints..."

echo "Testing health endpoint..."
HEALTH_RESPONSE=$(ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "curl -s http://localhost:5000/api/health")
echo "Health response: $HEALTH_RESPONSE"

echo "Testing orders endpoint..."
ORDER_TEST_RESPONSE=$(ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" 'curl -s -X POST http://localhost:5000/api/orders -H "Content-Type: application/json" -d "{\"customerInfo\":{\"fullName\":\"Test User\",\"phone1\":\"**********\",\"address\":\"Test Address\",\"city\":\"Colombo\",\"zodiacSign\":\"මේෂ\"},\"items\":[{\"name\":\"Kubera Card\",\"price\":1299,\"quantity\":1}],\"totalAmount\":1299}"')
echo "Order test response: $ORDER_TEST_RESPONSE"

# Test external API access
echo "🌐 Testing external API access..."
EXTERNAL_HEALTH=$(curl -s https://kubera.help/api/health)
echo "External health response: $EXTERNAL_HEALTH"

echo ""
echo "🎉 Server deployment completed!"
echo "✅ Order processing functionality should now be available"
echo "🔗 Test the order placement at: https://kubera.help"
echo ""
echo "📊 Next steps:"
echo "1. Test order placement on the website"
echo "2. Check Telegram notifications"
echo "3. Monitor PM2 logs: pm2 logs kubera-backend"
