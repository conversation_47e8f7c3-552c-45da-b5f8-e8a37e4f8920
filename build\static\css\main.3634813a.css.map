{"version": 3, "file": "static/css/main.3634813a.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAClC,kBAAmB,CALnB,qJAEY,CAHZ,QASF,CAEA,KACE,uEAEF,CAMA,KACE,sBACF,CAGA,oBACE,SACF,CAEA,0BACE,oBACF,CAEA,0BACE,oBAAmC,CACnC,iBACF,CAEA,gCACE,oBACF,CAGA,YACE,oBAAmC,CACnC,UACF,CAEA,iBACE,oBAAmC,CACnC,UACF,CCpDA,EAUE,0BAA2B,CAC3B,uCAAwC,CARxC,qBAAsB,CAFtB,QAAS,CACT,SAUF,CAEA,OATE,wBAAyB,CAGzB,gBAiBF,CAXA,KAEE,8DAA0E,CAC1E,aAAc,CAFd,wCAA4C,CAG5C,gBAAiB,CACjB,iBAMF,CAGA,IACE,sBAAuB,CACvB,qBAAsB,CACtB,mBAAoB,CACpB,iBAAkB,CAClB,cAAe,CACf,mBAKF,CAGA,mCAPE,wBAAyB,CAGzB,gBASF,CAQA,mBALE,gBAAiB,CACjB,iBAaF,CATA,cAIE,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,sBAAuB,CAGvB,eAAgB,CAFhB,YAGF,CAEA,gBAEE,kBAAmB,CAEnB,iBAAkB,CAHlB,iBAAkB,CAElB,SAEF,CAEA,YASE,sDAAuD,CANvD,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAQhB,kBAAmB,CAFnB,oBAAqB,CAJrB,oEAOF,CAEA,UAEE,aAAc,CADd,gBAAiB,CAEjB,eAAgB,CAGhB,eAAgB,CAFhB,kBAAmB,CACnB,gCAEF,CAEA,aAEE,aAAc,CADd,gBAAiB,CAGjB,eAAgB,CAEhB,kBAAwB,CAHxB,eAAgB,CAEhB,iBAAkB,CAElB,+BACF,CAEA,iBAOE,kCAA2B,CAA3B,0BAA2B,CAJ3B,sDAA8F,CAC9F,0BAAyC,CACzC,kBAAmB,CACnB,oBAAqB,CALrB,eAAgB,CAChB,iBAMF,CAEA,eACE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAChB,8BACF,CAGA,qBAGE,eAAW,CAFX,YAAa,CAEb,UAAW,CADX,wDAA2D,CAE3D,gBAAiB,CAGjB,cAAe,CAFf,UAAW,CACX,SAEF,CAGA,iBAQE,kCAA2B,CAA3B,0BAA2B,CAP3B,8BAAgD,CAChD,0BAAyC,CACzC,kBAAmB,CAUnB,2EAGyC,CAJzC,aAAc,CALd,cAAe,CAGf,eAAgB,CANhB,cAAe,CAKf,iBAAkB,CAJlB,eAAgB,CAMhB,oBAAqB,CALrB,oDAWF,CAEA,wBAOE,uDAAqF,CANrF,UAAW,CAKX,WAAY,CAFZ,UAAW,CAKX,mBAAoB,CAPpB,iBAAkB,CAClB,KAAM,CAKN,mBAAqB,CAHrB,UAKF,CAEA,8BACE,SACF,CAEA,uBAEE,sBAAqC,CACrC,0EAGwC,CALxC,2BAMF,CAEA,WAME,wDAAiF,CADjF,WAAY,CAFZ,SAAU,CAIV,SAAU,CAEV,mBAAoB,CARpB,iBAAkB,CAClB,QAAS,CAMT,2BAA6B,CAJ7B,UAMF,CAEA,kCACE,SACF,CAEA,YAME,uDAAqF,CADrF,UAAW,CAFX,MAAO,CAIP,UAAY,CANZ,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAGA,uBAEE,kBAAmB,CAGnB,iCAAgD,CAJhD,YAAa,CAEb,kBAAmB,CACnB,qBAEF,CAEA,mBAGE,aAAc,CAId,4CAAiD,CANjD,cAAe,CACf,mBAAoB,CAEpB,iDAEkC,CAElC,uBACF,CAEA,0CACE,iDAEkC,CAClC,qBACF,CAEA,sBACE,QACF,CAEA,oBAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAIhB,eAAgB,CAFhB,mBAAqB,CACrB,gCAEF,CAEA,oBAEE,aAAc,CADd,gBAAiB,CAEjB,eAAgB,CAChB,UACF,CAEA,gBAEE,aAAe,CADf,oBAEF,CAEA,YAGE,kBAAmB,CAGnB,iCAAkD,CALlD,YAAa,CACb,6BAA8B,CAE9B,mBAAqB,CACrB,eAEF,CAEA,uBACE,kBACF,CAEA,cAEE,aAAc,CADd,gBAAkB,CAElB,eAAgB,CAChB,UACF,CAEA,cAEE,aAAc,CADd,gBAAkB,CAElB,eAAgB,CAChB,gBAAiB,CACjB,8BACF,CAEA,oBAME,oBAA8B,CAK9B,0BAA2C,CAJ3C,kBAAmB,CALnB,aAAc,CADd,cAAe,CAEf,iBAAkB,CAMlB,eAAgB,CALhB,oBAAqB,CACrB,YAOF,CAEA,iCAJE,iCAA0B,CAA1B,yBAeF,CAXA,aAGE,kBAAmB,CAInB,oBAA+B,CAF/B,8BAA6C,CAJ7C,YAAa,CACb,6BAA8B,CAM9B,2BAAoC,CACpC,mBAEF,CAEA,aACE,cAAe,CAEf,eAEF,CAEA,2BALE,aAAc,CAEd,8BAQF,CALA,cACE,gBAAiB,CAEjB,6BAEF,CAEA,qCACE,yBACF,CAEA,oCACE,8BACF,CAGA,aACE,gBAAiB,CAGjB,eAAgB,CAFhB,YAAa,CACb,iBAEF,CAEA,aAIE,oBAAmC,CACnC,wBAAyB,CAGzB,kBAAmB,CAFnB,aAAc,CAId,wCAA4C,CAC5C,eAAgB,CARhB,SAAU,CAIV,oBAAsB,CANtB,iBAAkB,CAQlB,oBAAqB,CAPrB,QAAS,CAUT,uBAAyB,CACzB,UACF,CAEA,mBACE,oBAAmC,CACnC,0BACF,CAEA,gBAEE,aAAc,CADd,gBAAiB,CAGjB,gBAAiB,CAEjB,iBAAkB,CAHlB,iBAAkB,CAElB,SAEF,CAEA,cAGE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,kBAAmB,CACnB,8BACF,CAEA,iBAEE,aAAc,CADd,gBAAiB,CAEjB,kBACF,CAEA,mBAME,kCAA2B,CAA3B,0BAA2B,CAL3B,oBAAqC,CACrC,0BAAyC,CACzC,kBAAmB,CAEnB,aAAc,CAEd,gBAAiB,CAHjB,YAAa,CAIb,UACF,CAEA,iBAEE,aAAc,CADd,cAAe,CAEf,oBACF,CAEA,mBAGE,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAEhB,eACF,CAEA,8BACE,0BAA4B,CAE5B,eAAgB,CADhB,UAEF,CAMA,oDACE,yBACF,CAGA,mBAQE,eAAgB,CADhB,mBAAoB,CANpB,cAAe,CAKf,SAGF,CAEA,8BANE,WAAY,CAFZ,MAAO,CADP,KAAM,CAEN,UAkBF,CAXA,WAOE,6DAA+D,CAE/D,gBAAiB,CACjB,sBAAuB,CAJvB,WAAa,CALb,iBAAkB,CAOlB,uBAGF,CAGA,8BAEE,6DAA+D,CAD/D,WAAa,CAEb,qBACF,CAGA,0BASE,sDAAuD,CAFvD,kEAA2F,CAN3F,UAAW,CAKX,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAAW,CAGX,SAEF,CAaA,SAEE,aAAc,CACd,iBACF,CAEA,gBALE,gBAQF,CAHA,OACE,aAEF,CAGA,sBACE,GACE,oEAIF,CACA,IACE,oEAIF,CACA,GACE,oEAIF,CACF,CAEA,gBACE,GACE,8BACF,CACA,GACE,iDACF,CACF,CAEA,wBACE,MACE,oCACF,CACA,IACE,wCACF,CACA,IACE,wCACF,CACA,IACE,yCACF,CACF,CAEA,qBACE,8CACF,CAEA,mCACE,mBACF,CAEA,mCACE,qBACF,CAEA,mCACE,qBACF,CAEA,iBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAEA,UACE,uCACF,CAGA,qBAEE,KAMF,CAGA,sCANE,WAAY,CAFZ,MAAO,CAIP,mBAAoB,CANpB,cAAe,CAGf,UAAW,CAEX,SAaF,CARA,iBAEE,QAMF,CAEA,OAOE,sCAAuC,CAFvC,wDAAgF,CAChF,iBAAkB,CAJlB,YAAa,CAEb,YAAa,CAHb,iBAAkB,CAElB,WAKF,CAEA,qBACE,GAEE,UAAY,CADZ,gCAEF,CACA,GAEE,SAAU,CADV,qCAEF,CACF,CAKA,0BACE,cACE,cACF,CAEA,aAEE,UAAW,CADX,wDAEF,CAEA,YACE,cACF,CAEA,cACE,gBACF,CACF,CAGA,yBAEE,WACE,WACF,CAEA,YACE,gBAAiB,CAEjB,kBAAmB,CADnB,kBAEF,CAEA,UACE,gBAAiB,CACjB,oBAAqB,CACrB,cACF,CAEA,aACE,cAAe,CAEf,aAAc,CADd,gBAEF,CAEA,iBACE,oBAA0B,CAC1B,oBACF,CAEA,eACE,cACF,CAEA,qBAEE,UAAW,CADX,yBAA0B,CAE1B,cACF,CAEA,iBACE,mBACF,CAEA,aACE,yBAAkC,CAClC,mBACF,CAEA,uBACE,qBAAsB,CAEtB,oBAAqB,CADrB,iBAEF,CAEA,mBACE,gBAAiB,CAEjB,kBAAmB,CADnB,cAEF,CAEA,oBACE,gBACF,CAEA,oBACE,cACF,CAEA,YACE,qBAAsB,CAEtB,SACF,CAEA,0BAJE,iBAMF,CAEA,oBACE,gBAAkB,CAClB,iBACF,CAEA,cACE,gBAAiB,CACjB,mBACF,CAEA,iBACE,gBAAiB,CACjB,kBACF,CAEA,mBAEE,eAAgB,CADhB,mBAEF,CAEA,iBACE,gBAAiB,CACjB,kBACF,CAEA,mBACE,gBAAiB,CACjB,eACF,CAEA,aAIE,eAAiB,CAFjB,SAAU,CACV,kBAAoB,CAFpB,QAIF,CAEA,gBACE,kBACF,CACF,CAGA,yBAKE,2BACE,kBACF,CAGA,WACE,WACF,CAEA,YACE,gBAAiB,CACjB,eACF,CAEA,UACE,gBAAiB,CACjB,eACF,CAEA,aACE,gBAAkB,CAClB,eACF,CAEA,iBACE,mBAA4B,CAC5B,kBACF,CAEA,eACE,eACF,CAEA,qBAEE,QAAS,CADT,yBAA0B,CAE1B,eACF,CAEA,iBACE,mBACF,CAEA,aACE,yBAAgC,CAChC,YACF,CAEA,mBACE,cAAe,CACf,mBACF,CAEA,oBACE,gBACF,CAEA,oBACE,eACF,CAEA,gBACE,kBACF,CAEA,YACE,mBAAqB,CACrB,eACF,CAEA,4BACE,gBACF,CAEA,oBACE,eAAiB,CAEjB,kBAAmB,CADnB,aAEF,CAEA,aACE,iBACF,CAEA,aACE,eACF,CAEA,cACE,cAAe,CACf,eACF,CAEA,iBACE,gBAAiB,CACjB,oBACF,CAEA,mBAEE,aAAc,CADd,mBAEF,CAEA,iBACE,gBACF,CAEA,mBACE,cAAe,CACf,eACF,CAEA,aAIE,gBAAkB,CAFlB,UAAY,CACZ,mBAAsB,CAFtB,SAIF,CAEA,gBACE,gBACF,CAEA,UACE,2BACF,CAEA,cAEE,yBAA4B,CAD5B,4BAEF,CAEA,mBACE,yBAA2B,CAC3B,wBACF,CAEA,qBACE,wBACF,CACF,CAGA,yBACE,YACE,gBACF,CAEA,aACE,SACF,CAEA,aACE,kBACF,CAEA,aACE,cACF,CAEA,aACE,eACF,CAEA,gBACE,eACF,CAEA,cACE,gBACF,CAEA,mBACE,oBACF,CACF,CAGA,sDACE,cACE,YACF,CAEA,YACE,cAAe,CACf,mBACF,CAEA,UACE,cAAe,CACf,kBACF,CAEA,aACE,eACF,CAEA,aACE,SACF,CAEA,aACE,YACF,CACF,CAGA,yCACE,aACE,6BACF,CAEA,oBACE,oBACF,CAMA,yCACE,oBACF,CACF,CAGA,mBAGE,aAAc,CADd,gBAAiB,CADjB,iBAGF,CAEA,mBACE,YAAa,CACb,sBAAuB,CACvB,kBAAmB,CAEnB,iBAAkB,CADlB,SAEF,CAEA,gBAEE,kBAAmB,CAGnB,oBAAqC,CACrC,0BAAyC,CACzC,kBAAmB,CACnB,aAAc,CAKd,cAAe,CAZf,YAAa,CASb,gBAAiB,CACjB,eAAgB,CARhB,QAAS,CAYT,eAAgB,CAXhB,iBAAkB,CAUlB,iBAAkB,CALlB,oBAAqB,CAGrB,uBAIF,CAEA,sBAEE,sBAAqC,CACrC,gCAA0C,CAF1C,0BAGF,CAEA,0BACE,gBACF,CAEA,2BACE,6BACF,CAEA,iCACE,yBACF,CAEA,mBAEE,oBAAqC,CACrC,0BAAyC,CACzC,kBAAmB,CACnB,aAAc,CAEd,cAAe,CADf,cAAe,CAGf,kBAAmB,CARnB,oBAAsB,CAOtB,uBAEF,CAEA,yBAEE,sBAAqC,CADrC,0BAEF,CAEA,wBAEE,YAAa,CACb,sBAAuB,CAFvB,kBAGF,CAEA,qBAIE,+BAAiC,CADjC,aAAc,CADd,eAAgB,CADhB,UAIF,CAEA,gBACE,kBAAmB,CACnB,iBACF,CAEA,eAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAEhB,kBAAmB,CADnB,8BAEF,CAEA,cAGE,aAAc,CADd,gBAAiB,CADjB,eAGF,CAEA,gBACE,oBAAqB,CACrB,kBACF,CAGA,aACE,sDACF,CAEA,gBACE,iBACF,CAEA,iBAEE,aAAc,CADd,gBAAiB,CAGjB,eAAgB,CADhB,kBAEF,CAEA,iBAGE,oBAAkC,CAIlC,0BAAwC,CAFxC,kBAAmB,CAHnB,UAAc,CAMd,sCAA0C,CAP1C,gBAAiB,CASjB,8BACF,CAEA,wCAJE,eAAgB,CAHhB,aAAc,CAFd,cAkBF,CATA,uBAGE,oBAAoC,CAIpC,0BAA0C,CAF1C,kBAAmB,CAHnB,aAAc,CADd,gBAQF,CAEA,gBAGE,oBAAoC,CAIpC,0BAA0C,CAF1C,kBAAmB,CAHnB,aAAc,CADd,gBAAiB,CAQjB,eAAgB,CAHhB,aAAc,CAFd,cAAe,CAIf,kBAEF,CAGA,kBACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,gBACE,oBAAqC,CAGrC,6BAA8B,CAD9B,kBAAmB,CADnB,cAGF,CAEA,iBAEE,aAAc,CADd,gBAAiB,CAGjB,eAAgB,CADhB,mBAEF,CAEA,kBAEE,aAAc,CADd,eAEF,CAGA,eACE,sDACF,CAEA,eAEE,eAAW,CADX,YAAa,CACb,UACF,CAEA,cAEE,sBAAuB,CAEvB,oBAAqC,CAGrC,0BAAyC,CADzC,kBAAmB,CALnB,YAAa,CAEb,QAAS,CAET,cAAe,CAGf,uBACF,CAEA,oBAEE,sBAAqC,CACrC,2BAAyC,CAFzC,0BAGF,CAEA,cACE,cAAe,CACf,cAAe,CACf,iBACF,CAEA,oBAEE,aAAc,CADd,gBAAiB,CAGjB,eAAgB,CADhB,mBAEF,CAEA,mBAEE,aAAc,CACd,eAAgB,CAFhB,eAGF,CAGA,sBACE,sDAAuF,CACvF,sBACF,CAEA,gBACE,oBAAkC,CAGlC,0BAAwC,CADxC,kBAAmB,CADnB,YAGF,CAEA,kBACE,aAAc,CACd,eAAgB,CAEhB,oBAAqB,CADrB,kBAEF,CAEA,6BACE,eACF,CAGA,qBAGE,gBAAiB,CADjB,gBAAiB,CAEjB,cAAe,CAEf,iBAAkB,CALlB,UAAW,CAIX,SAEF,CAEA,uBAGE,kBAAmB,CADnB,eAAgB,CADhB,iBAGF,CAEA,gBAEE,kBAAmB,CADnB,iBAEF,CAEA,eAOE,sDAAuD,CALvD,aAAc,CADd,gBAAiB,CAEjB,kBAAmB,CACnB,iDAIF,CAEA,kBAEE,aAAc,CADd,gBAAiB,CAEjB,eAAgB,CAChB,gCACF,CAGA,gBACE,aACF,CAEA,kBAGE,kBAAmB,CADnB,YAAa,CAEb,sBAAuB,CACvB,kBAAmB,CAJnB,iBAKF,CAEA,iBAME,0BAAyC,CAFzC,kBAAmB,CAGnB,mDAEkC,CANlC,YAAa,CAEb,eAAgB,CAJhB,iBAAkB,CAClB,WAQF,CAEA,eAEE,WAAY,CACZ,gBAAiB,CACjB,6BAA+B,CAH/B,UAIF,CAEA,qBACE,qBACF,CAEA,eAcE,oBAAqB,CARrB,4EAMC,CAPD,QAAS,CAQT,YAAa,CAEb,wBAAyB,CAZzB,MAAO,CAaP,YAAa,CAfb,iBAAkB,CAGlB,OAAQ,CAFR,KAeF,CAEA,iBAEE,kBAAmB,CADnB,YAAa,CAEb,QACF,CAEA,eACE,oBAA8B,CAM9B,0BAAyC,CAHzC,kBAAmB,CAFnB,aAAc,CAGd,eAAiB,CACjB,eAAgB,CAHhB,mBAKF,CAEA,YAYE,kBAAmB,CAFnB,kCAA2B,CAA3B,0BAA2B,CAT3B,oBAAmC,CACnC,0BAAyC,CAIzC,iBAAkB,CAHlB,aAAc,CAKd,cAAe,CAGf,YAAa,CAJb,gBAAiB,CAFjB,WAAY,CAQZ,sBAAuB,CACvB,aAAc,CALd,uBAAyB,CALzB,UAWF,CAEA,kBACE,oBAAmC,CACnC,sBAAqC,CAErC,6BAA4C,CAD5C,oBAEF,CAEA,aACE,YAAa,CAEb,SAAW,CADX,sBAAuB,CAEvB,eACF,CAEA,KAKE,oBAAmC,CADnC,0BAAyC,CADzC,iBAAkB,CAGlB,cAAe,CAJf,WAAY,CAKZ,uBAAyB,CANzB,UAOF,CAEA,YACE,oBAAmC,CACnC,oBAAmC,CACnC,6BACF,CAEA,WACE,oBAAmC,CACnC,oBACF,CAGA,iBACE,aACF,CAEA,kBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAA2D,CAE3D,kBACF,CAEA,cAEE,kBAAmB,CAGnB,oBAAqC,CACrC,0BAAyC,CACzC,kBAAmB,CANnB,YAAa,CAEb,SAAW,CACX,YAAa,CAIb,uBACF,CAEA,oBACE,oBAAqC,CACrC,sBAAqC,CACrC,0BACF,CAEA,cAEE,aAAc,CADd,gBAEF,CAEA,cACE,aAAc,CACd,gBAAkB,CAClB,eACF,CAGA,iBAEE,aAAc,CADd,iBAEF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAGb,QAAS,CADT,sBAAuB,CAEvB,mBACF,CAEA,gBAEE,UAAW,CADX,gBAAiB,CAEjB,4BACF,CAEA,eAEE,aAAc,CADd,cAAe,CAEf,eAAgB,CAChB,8BACF,CAEA,gBAOE,2BAA4B,CAN5B,kDAAqD,CAGrD,kBAAmB,CAFnB,UAAY,CAGZ,eAAiB,CACjB,eAAgB,CAHhB,mBAKF,CAEA,YACE,aAAc,CACd,eAAiB,CACjB,iBACF,CAGA,iBAEE,eAAgB,CADhB,iBAEF,CAEA,aAWE,kBAAmB,CAVnB,kDAAqD,CACrD,WAAY,CAGZ,kBAAmB,CAQnB,kDAEkC,CAZlC,aAAc,CAKd,cAAe,CAEf,mBAAoB,CAJpB,gBAAiB,CACjB,eAAgB,CAKhB,QAAS,CAIT,oBAAqB,CAZrB,qBAAsB,CAKtB,uBAQF,CAEA,mBAKE,kDAAqD,CAHrD,mDAEkC,CAHlC,sCAKF,CAEA,UACE,gBACF,CAEA,UACE,wCACF,CAEA,WACE,gBAAiB,CACjB,6BACF,CAEA,8BACE,yBACF,CAEA,cACE,YAAa,CAGb,cAAe,CADf,QAAS,CADT,sBAGF,CAEA,+BAGE,kBAAmB,CAEnB,aAAc,CAHd,YAAa,CAIb,gBAAkB,CAFlB,SAGF,CAEA,6BAGE,aAAc,CADd,gBAEF,CAGA,kBAQE,kBAAmB,CAInB,iCAA0B,CAA1B,yBAA0B,CAN1B,gBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAQP,YAAa,CAVb,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YAGF,CAEA,oBAGE,eAAgB,CADhB,eAAgB,CAEhB,eAAgB,CAChB,iBAAkB,CAJlB,UAKF,CAEA,iBAGE,kBAAmB,CAGnB,iCAAgD,CALhD,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,mBAEF,CAEA,oBACE,aAAc,CACd,gBAAiB,CACjB,QACF,CAEA,WAUE,kBAAmB,CATnB,eAAgB,CAChB,WAAY,CAUZ,iBAAkB,CATlB,aAAc,CAEd,cAAe,CAIf,YAAa,CALb,cAAe,CAIf,WAAY,CAGZ,sBAAuB,CALvB,SAAU,CAOV,uBAAyB,CANzB,UAOF,CAEA,iBACE,oBAAmC,CACnC,oBACF,CAGA,gBACE,YAAa,CACb,6BAA8B,CAC9B,kBAAmB,CACnB,iBACF,CAEA,uBAOE,oBAAmC,CANnC,UAAW,CAKX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,QAAS,CAKT,SACF,CAEA,MAEE,qBAAsB,CAEtB,iBAAkB,CAClB,SACF,CAEA,mBALE,kBAAmB,CAFnB,YAoBF,CAbA,aAIE,oBAAoC,CACpC,0BAAyC,CAFzC,iBAAkB,CAMlB,UAAW,CACX,eAAgB,CARhB,WAAY,CAMZ,sBAAuB,CAGvB,mBAAqB,CACrB,uBAAyB,CAXzB,UAYF,CAEA,0BACE,oBAAmC,CACnC,sBAAqC,CACrC,aACF,CAEA,6BACE,oBAAmC,CACnC,oBAAmC,CACnC,aACF,CAEA,YAEE,UAAW,CADX,eAAiB,CAEjB,yBACF,CAMA,qDACE,aACF,CAGA,kBACE,eACF,CAEA,eACE,gCACF,CAEA,YACE,aAAc,CACd,gBAAiB,CAEjB,iBACF,CAEA,wBAJE,oBAMF,CAEA,UAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,kBAEE,aAAc,CADd,aAAc,CAEd,gBAAkB,CAElB,eAAgB,CADhB,mBAEF,CAEA,0DAKE,oBAAqC,CACrC,0BAAyC,CACzC,iBAAkB,CAClB,aAAc,CAGd,wCAA4C,CAF5C,gBAAkB,CALlB,kBAAoB,CAMpB,uBAAyB,CAPzB,UASF,CAEA,4EAKE,oBAAqC,CADrC,sBAAqC,CAErC,6BAA4C,CAH5C,YAIF,CAEA,iEAEE,UACF,CAEA,mBACE,cACF,CAEA,0BACE,kBAAmB,CACnB,aAAc,CACd,aACF,CAEA,cAME,8BAA6C,CAL7C,YAAa,CAEb,QAAS,CADT,6BAA8B,CAE9B,eAAgB,CAChB,kBAEF,CAEA,4BAQE,WAAY,CALZ,iBAAkB,CAGlB,cAAe,CAGf,wCAA4C,CAL5C,gBAAkB,CAClB,eAAgB,CAHhB,oBAAsB,CAKtB,uBAGF,CAEA,aACE,kDAAqD,CACrD,aACF,CAEA,kCACE,kDAAqD,CAErD,+BAA8C,CAD9C,0BAEF,CAEA,sBAEE,kBAAmB,CADnB,UAEF,CAEA,eACE,oBAAoC,CAEpC,0BAAyC,CADzC,aAEF,CAEA,qBACE,oBAAqC,CACrC,sBACF,CAGA,eACE,oBAAqC,CACrC,0BAAyC,CACzC,kBAAmB,CAEnB,oBAAqB,CADrB,cAEF,CAEA,iBAEE,kBAAmB,CAInB,iCAAgD,CALhD,YAAa,CAEb,QAAS,CACT,oBAAqB,CACrB,mBAEF,CAEA,eAKE,0BAAyC,CADzC,iBAAkB,CAFlB,WAAY,CACZ,gBAAiB,CAFjB,UAKF,CAEA,iBACE,aAAc,CACd,mBACF,CAEA,gBACE,aAAc,CACd,eAAiB,CACjB,mBACF,CAEA,YAEE,kBAAmB,CADnB,YAAa,CAEb,SACF,CAEA,4BACE,UAAW,CAEX,eAAiB,CADjB,4BAEF,CAEA,2BACE,aAAc,CAEd,gBAAiB,CADjB,eAEF,CAEA,aACE,aACF,CAEA,WAGE,kBAAmB,CAEnB,aAAc,CAJd,YAAa,CACb,6BAA8B,CAE9B,mBAEF,CAEA,iBACE,aAAc,CACd,eACF,CAEA,aAKE,8BAA6C,CAF7C,aAAc,CAFd,gBAAiB,CACjB,eAAgB,CAIhB,gBAAkB,CAFlB,iBAGF,CAGA,uCAEE,oBAAqC,CACrC,0BAAyC,CACzC,kBAAmB,CAEnB,oBAAqB,CADrB,cAEF,CAEA,6CAEE,aAAc,CACd,kBACF,CAEA,gBACE,aAAc,CAEd,eAAgB,CADhB,mBAEF,CAEA,gBAEE,kBAAmB,CAEnB,aAAc,CAHd,YAAa,CAEb,QAEF,CAEA,cACE,aAAc,CACd,eAAiB,CACjB,iBACF,CAGA,cACE,iBACF,CAEA,cAGE,+BAAgC,CAFhC,cAAe,CACf,kBAEF,CAEA,iBACE,aAAc,CACd,gBAAiB,CACjB,eAAgB,CAChB,oBACF,CAEA,oBACE,oBAAqC,CACrC,0BAAyC,CACzC,kBAAmB,CACnB,cAEF,CAEA,mCAHE,oBAKF,CAEA,iBACE,aAAc,CACd,mBACF,CAEA,eACE,aAAc,CACd,kBACF,CAEA,eAEE,aAAc,CACd,eAAgB,CAFhB,eAGF,CAEA,eACE,mBACF,CAGA,kBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,kBACE,kBACE,uBACF,CACA,IACE,2BACF,CACA,IACE,0BACF,CACF,CAGA,eACE,eAAgB,CAEhB,cAAe,CADf,iBAEF,CAEA,gCACE,oBAAqC,CAGrC,0BAAyC,CADzC,kBAAmB,CAEnB,oBAAqB,CAHrB,mBAIF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,UACE,0CACF,CAEA,oBACE,MACE,uBACF,CACA,IACE,2BACF,CACF,CAEA,iBACE,GAEE,+BAAyC,CADzC,kBAEF,CACA,IAEE,gCAA+C,CAD/C,qBAEF,CACA,GAEE,+BAAyC,CADzC,kBAEF,CACF,CAGA,yBACE,mBACE,kBACF,CAGA,qBAEE,gBAAiB,CADjB,cAEF,CAEA,eACE,cACF,CAEA,kBACE,gBACF,CAEA,iBAEE,YAAa,CADb,WAEF,CAEA,YAGE,gBAAiB,CADjB,WAAY,CAEZ,cAAgB,CAHhB,UAIF,CAEA,kBAEE,SAAW,CADX,yBAEF,CAEA,cACE,aACF,CAEA,eACE,gBACF,CAEA,aAEE,gBAAiB,CADjB,iBAEF,CAEA,cACE,qBAAsB,CACtB,QACF,CAEA,eACE,gBACF,CAEA,cACE,cACF,CAEA,iBACE,gBAAiB,CACjB,YACF,CAEA,uBACE,gBAAiB,CACjB,YACF,CAEA,gBACE,cAAe,CACf,YACF,CAEA,cACE,qBAAsB,CACtB,iBACF,CAEA,cACE,cACF,CAEA,gBAEE,cAAe,CADf,oBAEF,CACF,CAEA,yBACE,YACE,gBACF,CAEA,UACE,gBACF,CAGA,qBAEE,kBAAmB,CADnB,eAEF,CAEA,eACE,gBACF,CAEA,kBACE,cACF,CAEA,iBAEE,YAAa,CADb,WAEF,CAEA,YAGE,cAAe,CADf,WAAY,CAEZ,cAAgB,CAHhB,UAIF,CAEA,eACE,gBACF,CAEA,aAEE,cAAe,CADf,oBAEF,CAEA,cACE,gBACF,CAGA,oBACE,YAAc,CACd,eACF,CAEA,oBACE,gBACF,CAEA,gBACE,oBACF,CAEA,aAGE,eAAiB,CADjB,WAAY,CADZ,UAGF,CAEA,YACE,eACF,CAEA,UACE,yBACF,CAEA,cACE,qBAAsB,CACtB,SACF,CAEA,4BAGE,YAAa,CADb,UAEF,CAEA,iBACE,qBAAsB,CACtB,iBACF,CAEA,cACE,qBAAsB,CACtB,QACF,CAEA,eACE,gBACF,CAEA,qBACE,cACF,CAMA,8BACE,YACF,CAEA,gBACE,cACF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: 'Noto Sans Sinhala', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  background: #0a0a0a;\n  color: #f4d03f;\n  overflow-x: hidden;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n* {\n  box-sizing: border-box;\n}\n\nhtml {\n  scroll-behavior: smooth;\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 8px;\n}\n\n::-webkit-scrollbar-track {\n  background: rgba(26, 26, 46, 0.5);\n}\n\n::-webkit-scrollbar-thumb {\n  background: rgba(244, 208, 63, 0.3);\n  border-radius: 4px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: rgba(244, 208, 63, 0.5);\n}\n\n/* Selection styles */\n::selection {\n  background: rgba(244, 208, 63, 0.3);\n  color: #fff;\n}\n\n::-moz-selection {\n  background: rgba(244, 208, 63, 0.3);\n  color: #fff;\n}", "* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n  /* Disable text selection */\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  /* Disable right-click context menu */\n  -webkit-touch-callout: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\nbody {\n  font-family: 'Noto Sans Sinhala', sans-serif;\n  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);\n  color: #f4d03f;\n  min-height: 100vh;\n  overflow-x: hidden;\n  /* Additional protection */\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n/* Disable image dragging and selection */\nimg {\n  -webkit-user-drag: none;\n  -khtml-user-drag: none;\n  -moz-user-drag: none;\n  -o-user-drag: none;\n  user-drag: none;\n  pointer-events: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n/* Disable selection on all text elements */\np, h1, h2, h3, h4, h5, h6, span, div, a {\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\n.App {\n  min-height: 100vh;\n  position: relative;\n}\n\n/* Landing Page Styles */\n.landing-page {\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 2rem;\n  position: relative;\n  overflow: hidden;\n}\n\n.landing-header {\n  text-align: center;\n  margin-bottom: 4rem;\n  z-index: 2;\n  position: relative;\n}\n\n.main-title {\n  font-size: 3.8rem;\n  font-weight: 700;\n  color: #f4d03f;\n  text-shadow:\n    0 0 20px rgba(244, 208, 63, 0.6),\n    0 0 40px rgba(244, 208, 63, 0.4),\n    0 0 60px rgba(244, 208, 63, 0.2);\n  margin-bottom: 1.5rem;\n  animation: divineGlow 3s ease-in-out infinite alternate;\n  letter-spacing: 2px;\n}\n\n.subtitle {\n  font-size: 1.8rem;\n  color: #e8f4fd;\n  font-weight: 400;\n  margin-bottom: 2rem;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n  line-height: 1.4;\n}\n\n.description {\n  font-size: 1.2rem;\n  color: #d5dbdb;\n  max-width: 700px;\n  line-height: 1.8;\n  text-align: center;\n  margin: 0 auto 2rem auto;\n  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);\n}\n\n.divine-blessing {\n  margin-top: 2rem;\n  padding: 1rem 2rem;\n  background: linear-gradient(135deg, rgba(244, 208, 63, 0.1) 0%, rgba(244, 208, 63, 0.05) 100%);\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 25px;\n  display: inline-block;\n  backdrop-filter: blur(10px);\n}\n\n.blessing-text {\n  color: #f4d03f;\n  font-size: 1.1rem;\n  font-weight: 500;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.5);\n}\n\n/* Premium Zodiac Grid */\n.premium-zodiac-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n  gap: 2.5rem;\n  max-width: 1400px;\n  width: 100%;\n  z-index: 2;\n  padding: 0 1rem;\n}\n\n/* Dark Glass Card Design - Consistent with other components */\n.dark-glass-card {\n  background: rgba(255, 255, 255, 0.02) !important;\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 20px;\n  padding: 2.5rem;\n  text-align: left;\n  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);\n  cursor: pointer;\n  backdrop-filter: blur(15px);\n  position: relative;\n  overflow: hidden;\n  text-decoration: none;\n  color: inherit;\n  box-shadow:\n    0 8px 32px rgba(0, 0, 0, 0.3),\n    0 2px 8px rgba(244, 208, 63, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.05);\n}\n\n.dark-glass-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(90deg, transparent, rgba(244, 208, 63, 0.1), transparent);\n  transition: left 0.5s;\n  pointer-events: none;\n}\n\n.dark-glass-card:hover::before {\n  left: 100%;\n}\n\n.dark-glass-card:hover {\n  transform: translateY(-10px);\n  border-color: rgba(244, 208, 63, 0.5);\n  box-shadow:\n    0 20px 60px rgba(0, 0, 0, 0.4),\n    0 10px 30px rgba(244, 208, 63, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n}\n\n.card-glow {\n  position: absolute;\n  top: -50%;\n  left: -50%;\n  width: 200%;\n  height: 200%;\n  background: radial-gradient(circle, rgba(244, 208, 63, 0.08) 0%, transparent 70%);\n  opacity: 0;\n  transition: opacity 0.4s ease;\n  pointer-events: none;\n}\n\n.dark-glass-card:hover .card-glow {\n  opacity: 1;\n}\n\n.card-shine {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background: linear-gradient(90deg, transparent, rgba(244, 208, 63, 0.6), transparent);\n  opacity: 0.7;\n}\n\n/* Premium Card Content Styles */\n.zodiac-header-section {\n  display: flex;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1.5rem;\n  border-bottom: 1px solid rgba(244, 208, 63, 0.2);\n}\n\n.zodiac-icon-large {\n  font-size: 4rem;\n  margin-right: 1.5rem;\n  color: #f4d03f;\n  text-shadow:\n    0 0 20px rgba(244, 208, 63, 0.6),\n    0 0 40px rgba(244, 208, 63, 0.4);\n  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));\n  transition: all 0.3s ease;\n}\n\n.dark-glass-card:hover .zodiac-icon-large {\n  text-shadow:\n    0 0 30px rgba(244, 208, 63, 0.8),\n    0 0 60px rgba(244, 208, 63, 0.6);\n  transform: scale(1.05);\n}\n\n.zodiac-names-section {\n  flex: 1;\n}\n\n.sinhala-name-large {\n  font-size: 2rem;\n  font-weight: 700;\n  color: #f4d03f;\n  margin-bottom: 0.5rem;\n  text-shadow: 0 2px 10px rgba(244, 208, 63, 0.3);\n  line-height: 1.2;\n}\n\n.english-name-small {\n  font-size: 1.1rem;\n  color: #d5dbdb;\n  font-weight: 400;\n  opacity: 0.9;\n}\n\n.zodiac-details {\n  margin-bottom: 1.5rem;\n  space-y: 0.8rem;\n}\n\n.detail-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.8rem;\n  padding: 0.6rem 0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.detail-row:last-child {\n  border-bottom: none;\n}\n\n.detail-label {\n  font-size: 0.95rem;\n  color: #aeb6bf;\n  font-weight: 500;\n  opacity: 0.9;\n}\n\n.detail-value {\n  font-size: 0.95rem;\n  color: #f4d03f;\n  font-weight: 600;\n  text-align: right;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);\n}\n\n.zodiac-description {\n  font-size: 1rem;\n  color: #d5dbdb;\n  font-style: italic;\n  margin-bottom: 1.5rem;\n  padding: 1rem;\n  background: rgba(0, 0, 0, 0.1);\n  border-radius: 10px;\n  border-left: 3px solid rgba(244, 208, 63, 0.4);\n  line-height: 1.4;\n  backdrop-filter: blur(5px);\n  border: 1px solid rgba(255, 255, 255, 0.05);\n}\n\n.card-action {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-top: 1rem;\n  border-top: 1px solid rgba(244, 208, 63, 0.2);\n  margin-top: auto;\n  background: rgba(0, 0, 0, 0.05);\n  margin: 1rem -2.5rem -2.5rem -2.5rem;\n  padding: 1rem 2.5rem;\n  backdrop-filter: blur(5px);\n}\n\n.action-text {\n  font-size: 1rem;\n  color: #f4d03f;\n  font-weight: 600;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);\n}\n\n.action-arrow {\n  font-size: 1.2rem;\n  color: #f4d03f;\n  transition: transform 0.3s ease;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);\n}\n\n.dark-glass-card:hover .action-arrow {\n  transform: translateX(5px);\n}\n\n.dark-glass-card:hover .action-text {\n  text-shadow: 0 0 15px rgba(244, 208, 63, 0.5);\n}\n\n/* Zodiac Page Styles */\n.zodiac-page {\n  min-height: 100vh;\n  padding: 2rem;\n  position: relative;\n  overflow: hidden;\n}\n\n.back-button {\n  position: absolute;\n  top: 2rem;\n  left: 2rem;\n  background: rgba(244, 208, 63, 0.1);\n  border: 1px solid #f4d03f;\n  color: #f4d03f;\n  padding: 0.8rem 1.5rem;\n  border-radius: 25px;\n  text-decoration: none;\n  font-family: 'Noto Sans Sinhala', sans-serif;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  z-index: 10;\n}\n\n.back-button:hover {\n  background: rgba(244, 208, 63, 0.2);\n  transform: translateX(-5px);\n}\n\n.zodiac-content {\n  max-width: 1200px;\n  margin: 0 auto;\n  text-align: center;\n  padding-top: 4rem;\n  z-index: 2;\n  position: relative;\n}\n\n.zodiac-title {\n  font-size: 4rem;\n  font-weight: 700;\n  color: #f4d03f;\n  margin-bottom: 1rem;\n  text-shadow: 0 0 30px rgba(244, 208, 63, 0.6);\n}\n\n.zodiac-subtitle {\n  font-size: 1.5rem;\n  color: #d5dbdb;\n  margin-bottom: 3rem;\n}\n\n.horoscope-section {\n  background: rgba(255, 255, 255, 0.02);\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 20px;\n  padding: 3rem;\n  margin: 2rem 0;\n  backdrop-filter: blur(10px);\n  max-width: 1200px;\n  width: 100%;\n}\n\n.horoscope-title {\n  font-size: 2rem;\n  color: #f4d03f;\n  margin-bottom: 1.5rem;\n}\n\n.horoscope-content {\n  font-size: 1.2rem;\n  line-height: 1.8;\n  color: #d5dbdb;\n  text-align: left;\n}\n\n.structured-horoscope-display {\n  max-width: 1200px !important;\n  width: 100%;\n  text-align: left;\n}\n\n.horoscope-category-card {\n  text-align: left !important;\n}\n\n.horoscope-category-card p {\n  text-align: left !important;\n}\n\n/* Divine Background Image Styles */\n.divine-background {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n.god-image {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0.08;\n  filter: blur(0.5px) sepia(20%) saturate(150%) hue-rotate(30deg);\n  transition: all 0.3s ease;\n  object-fit: cover;\n  object-position: center;\n}\n\n/* Enhance the divine presence on hover */\n.zodiac-page:hover .god-image {\n  opacity: 0.12;\n  filter: blur(0.3px) sepia(25%) saturate(160%) hue-rotate(30deg);\n  transform: scale(1.02);\n}\n\n/* Additional divine glow effect */\n.divine-background::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background: radial-gradient(circle at center, rgba(244, 208, 63, 0.03) 0%, transparent 70%);\n  z-index: 1;\n  animation: divineGlow 4s ease-in-out infinite alternate;\n}\n\n@keyframes divineGlow {\n  0% {\n    opacity: 0.3;\n    transform: scale(1);\n  }\n  100% {\n    opacity: 0.6;\n    transform: scale(1.05);\n  }\n}\n\n.loading {\n  font-size: 1.1rem;\n  color: #aeb6bf;\n  font-style: italic;\n}\n\n.error {\n  color: #e74c3c;\n  font-size: 1.1rem;\n}\n\n/* Animations */\n@keyframes divineGlow {\n  0% {\n    text-shadow:\n      0 0 20px rgba(244, 208, 63, 0.6),\n      0 0 40px rgba(244, 208, 63, 0.4),\n      0 0 60px rgba(244, 208, 63, 0.2);\n  }\n  50% {\n    text-shadow:\n      0 0 30px rgba(244, 208, 63, 0.8),\n      0 0 60px rgba(244, 208, 63, 0.6),\n      0 0 90px rgba(244, 208, 63, 0.4);\n  }\n  100% {\n    text-shadow:\n      0 0 20px rgba(244, 208, 63, 0.6),\n      0 0 40px rgba(244, 208, 63, 0.4),\n      0 0 60px rgba(244, 208, 63, 0.2);\n  }\n}\n\n@keyframes glow {\n  from {\n    text-shadow: 0 0 20px rgba(244, 208, 63, 0.5);\n  }\n  to {\n    text-shadow: 0 0 30px rgba(244, 208, 63, 0.8), 0 0 40px rgba(244, 208, 63, 0.6);\n  }\n}\n\n@keyframes premiumFloat {\n  0%, 100% {\n    transform: translateY(0px) rotate(0deg);\n  }\n  25% {\n    transform: translateY(-5px) rotate(0.5deg);\n  }\n  50% {\n    transform: translateY(-10px) rotate(0deg);\n  }\n  75% {\n    transform: translateY(-5px) rotate(-0.5deg);\n  }\n}\n\n.premium-zodiac-card {\n  animation: premiumFloat 6s ease-in-out infinite;\n}\n\n.premium-zodiac-card:nth-child(even) {\n  animation-delay: -3s;\n}\n\n.premium-zodiac-card:nth-child(3n) {\n  animation-delay: -1.5s;\n}\n\n.premium-zodiac-card:nth-child(4n) {\n  animation-delay: -4.5s;\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n}\n\n.floating {\n  animation: float 3s ease-in-out infinite;\n}\n\n/* Particles Background */\n.particles-container {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  z-index: 1;\n  pointer-events: none;\n}\n\n/* Smoke Animation */\n.smoke-container {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.smoke {\n  position: absolute;\n  bottom: -50px;\n  width: 100px;\n  height: 100px;\n  background: radial-gradient(circle, rgba(244, 208, 63, 0.1) 0%, transparent 70%);\n  border-radius: 50%;\n  animation: smokeRise 8s linear infinite;\n}\n\n@keyframes smokeRise {\n  0% {\n    transform: translateY(0) scale(1);\n    opacity: 0.7;\n  }\n  100% {\n    transform: translateY(-100vh) scale(2);\n    opacity: 0;\n  }\n}\n\n/* Enhanced Mobile Responsive Design */\n\n/* Tablet styles */\n@media (max-width: 1024px) {\n  .landing-page {\n    padding: 1.5rem;\n  }\n  \n  .zodiac-grid {\n    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));\n    gap: 1.5rem;\n  }\n  \n  .main-title {\n    font-size: 3rem;\n  }\n  \n  .zodiac-title {\n    font-size: 3.5rem;\n  }\n}\n\n/* Mobile landscape and small tablets */\n@media (max-width: 768px) {\n  /* Adjust divine background for tablets */\n  .god-image {\n    opacity: 0.15;\n  }\n  \n  .main-title {\n    font-size: 2.8rem;\n    margin-bottom: 1rem;\n    letter-spacing: 1px;\n  }\n\n  .subtitle {\n    font-size: 1.4rem;\n    margin-bottom: 1.5rem;\n    padding: 0 1rem;\n  }\n\n  .description {\n    font-size: 1rem;\n    padding: 0 1.5rem;\n    max-width: 90%;\n  }\n\n  .divine-blessing {\n    margin: 1.5rem 1rem 0 1rem;\n    padding: 0.8rem 1.5rem;\n  }\n\n  .blessing-text {\n    font-size: 1rem;\n  }\n\n  .premium-zodiac-grid {\n    grid-template-columns: 1fr;\n    gap: 1.5rem;\n    padding: 0 1rem;\n  }\n\n  .dark-glass-card {\n    padding: 2rem 1.5rem;\n  }\n\n  .card-action {\n    margin: 1rem -1.5rem -2rem -1.5rem;\n    padding: 1rem 1.5rem;\n  }\n\n  .zodiac-header-section {\n    flex-direction: column;\n    text-align: center;\n    margin-bottom: 1.5rem;\n  }\n\n  .zodiac-icon-large {\n    font-size: 3.5rem;\n    margin-right: 0;\n    margin-bottom: 1rem;\n  }\n\n  .sinhala-name-large {\n    font-size: 1.8rem;\n  }\n\n  .english-name-small {\n    font-size: 1rem;\n  }\n\n  .detail-row {\n    flex-direction: column;\n    text-align: center;\n    gap: 0.3rem;\n  }\n\n  .detail-value {\n    text-align: center;\n  }\n\n  .zodiac-description {\n    font-size: 0.95rem;\n    text-align: center;\n  }\n  \n  .zodiac-title {\n    font-size: 2.5rem;\n    margin-bottom: 0.8rem;\n  }\n  \n  .zodiac-subtitle {\n    font-size: 1.3rem;\n    margin-bottom: 2rem;\n  }\n  \n  .horoscope-section {\n    padding: 2rem 1.5rem;\n    margin: 1.5rem 0;\n  }\n  \n  .horoscope-title {\n    font-size: 1.8rem;\n    margin-bottom: 1rem;\n  }\n  \n  .horoscope-content {\n    font-size: 1.1rem;\n    line-height: 1.7;\n  }\n  \n  .back-button {\n    top: 1rem;\n    left: 1rem;\n    padding: 0.6rem 1rem;\n    font-size: 0.9rem;\n  }\n  \n  .zodiac-content {\n    padding-top: 3.5rem;\n  }\n}\n\n/* Mobile portrait */\n@media (max-width: 480px) {\n  .landing-page {\n    padding: 1rem 0.5rem;\n  }\n  \n  .zodiac-page {\n    padding: 1rem 0.5rem;\n  }\n  \n  /* Adjust divine background for mobile */\n  .god-image {\n    opacity: 0.12;\n  }\n  \n  .main-title {\n    font-size: 2.2rem;\n    line-height: 1.2;\n  }\n\n  .subtitle {\n    font-size: 1.2rem;\n    padding: 0 0.5rem;\n  }\n\n  .description {\n    font-size: 0.95rem;\n    padding: 0 0.5rem;\n  }\n\n  .divine-blessing {\n    margin: 1rem 0.5rem 0 0.5rem;\n    padding: 0.6rem 1rem;\n  }\n\n  .blessing-text {\n    font-size: 0.9rem;\n  }\n\n  .premium-zodiac-grid {\n    grid-template-columns: 1fr;\n    gap: 1rem;\n    padding: 0 0.5rem;\n  }\n\n  .dark-glass-card {\n    padding: 1.5rem 1rem;\n  }\n\n  .card-action {\n    margin: 1rem -1rem -1.5rem -1rem;\n    padding: 1rem;\n  }\n\n  .zodiac-icon-large {\n    font-size: 3rem;\n    margin-bottom: 0.8rem;\n  }\n\n  .sinhala-name-large {\n    font-size: 1.5rem;\n  }\n\n  .english-name-small {\n    font-size: 0.9rem;\n  }\n\n  .zodiac-details {\n    margin-bottom: 1rem;\n  }\n\n  .detail-row {\n    margin-bottom: 0.6rem;\n    padding: 0.3rem 0;\n  }\n\n  .detail-label, .detail-value {\n    font-size: 0.85rem;\n  }\n\n  .zodiac-description {\n    font-size: 0.9rem;\n    padding: 0.8rem;\n    margin-bottom: 1rem;\n  }\n\n  .card-action {\n    padding-top: 0.8rem;\n  }\n\n  .action-text {\n    font-size: 0.9rem;\n  }\n  \n  .zodiac-title {\n    font-size: 2rem;\n    line-height: 1.2;\n  }\n  \n  .zodiac-subtitle {\n    font-size: 1.1rem;\n    margin-bottom: 1.5rem;\n  }\n  \n  .horoscope-section {\n    padding: 1.5rem 1rem;\n    margin: 1rem 0;\n  }\n  \n  .horoscope-title {\n    font-size: 1.5rem;\n  }\n  \n  .horoscope-content {\n    font-size: 1rem;\n    line-height: 1.6;\n  }\n  \n  .back-button {\n    top: 0.8rem;\n    left: 0.8rem;\n    padding: 0.5rem 0.8rem;\n    font-size: 0.85rem;\n  }\n  \n  .zodiac-content {\n    padding-top: 3rem;\n  }\n  \n  .controls {\n    margin-top: 1.5rem !important;\n  }\n  \n  .sound-toggle {\n    padding: 0.6rem 1rem !important;\n    font-size: 0.9rem !important;\n  }\n  \n  .spiritual-message {\n    margin-top: 2rem !important;\n    padding: 1.5rem !important;\n  }\n  \n  .spiritual-message p {\n    font-size: 1rem !important;\n  }\n}\n\n/* Very small screens */\n@media (max-width: 360px) {\n  .main-title {\n    font-size: 1.8rem;\n  }\n  \n  .zodiac-grid {\n    gap: 0.6rem;\n  }\n  \n  .zodiac-card {\n    padding: 1rem 0.6rem;\n  }\n  \n  .zodiac-icon {\n    font-size: 2rem;\n  }\n  \n  .zodiac-name {\n    font-size: 0.9rem;\n  }\n  \n  .zodiac-english {\n    font-size: 0.8rem;\n  }\n  \n  .zodiac-title {\n    font-size: 1.8rem;\n  }\n  \n  .horoscope-section {\n    padding: 1.2rem 0.8rem;\n  }\n}\n\n/* Landscape orientation adjustments */\n@media (max-height: 500px) and (orientation: landscape) {\n  .landing-page {\n    padding: 1rem;\n  }\n  \n  .main-title {\n    font-size: 2rem;\n    margin-bottom: 0.5rem;\n  }\n  \n  .subtitle {\n    font-size: 1rem;\n    margin-bottom: 1rem;\n  }\n  \n  .description {\n    font-size: 0.9rem;\n  }\n  \n  .zodiac-grid {\n    gap: 0.8rem;\n  }\n  \n  .zodiac-card {\n    padding: 1rem;\n  }\n}\n\n/* Touch-friendly improvements */\n@media (hover: none) and (pointer: coarse) {\n  .zodiac-card {\n    transition: transform 0.2s ease;\n  }\n  \n  .zodiac-card:active {\n    transform: scale(0.98);\n  }\n  \n  .back-button:active {\n    transform: scale(0.95);\n  }\n  \n  .sound-toggle:active {\n    transform: scale(0.95);\n  }\n}\n\n/* Kubera Guide Page Styles */\n.kubera-guide-page {\n  padding: 2rem 1rem;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.zodiac-navigation {\n  display: flex;\n  justify-content: center;\n  margin-bottom: 3rem;\n  z-index: 2;\n  position: relative;\n}\n\n.zodiac-nav-btn {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  padding: 1rem 2rem;\n  background: rgba(255, 255, 255, 0.02);\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 15px;\n  color: #f4d03f;\n  text-decoration: none;\n  font-size: 1.1rem;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n}\n\n.zodiac-nav-btn:hover {\n  transform: translateY(-3px);\n  border-color: rgba(244, 208, 63, 0.5);\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n}\n\n.zodiac-nav-btn .nav-icon {\n  font-size: 1.3rem;\n}\n\n.zodiac-nav-btn .nav-arrow {\n  transition: transform 0.3s ease;\n}\n\n.zodiac-nav-btn:hover .nav-arrow {\n  transform: translateX(5px);\n}\n\n.back-to-guide-btn {\n  padding: 0.8rem 1.5rem;\n  background: rgba(255, 255, 255, 0.02);\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 10px;\n  color: #f4d03f;\n  font-size: 1rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  margin-bottom: 2rem;\n}\n\n.back-to-guide-btn:hover {\n  transform: translateY(-2px);\n  border-color: rgba(244, 208, 63, 0.5);\n}\n\n.kubera-content-section {\n  margin-bottom: 3rem;\n  display: flex;\n  justify-content: center;\n}\n\n.kubera-content-card {\n  width: 100%;\n  max-width: 900px;\n  margin: 0 auto;\n  animation: fadeInUp 0.8s ease-out;\n}\n\n.content-header {\n  margin-bottom: 2rem;\n  text-align: center;\n}\n\n.content-title {\n  font-size: 2rem;\n  color: #f4d03f;\n  font-weight: 600;\n  text-shadow: 0 0 20px rgba(244, 208, 63, 0.4);\n  margin-bottom: 1rem;\n}\n\n.content-body {\n  line-height: 1.8;\n  font-size: 1.1rem;\n  color: #e8f4fd;\n}\n\n.content-body p {\n  margin-bottom: 1.5rem;\n  text-align: justify;\n}\n\n/* Mantra Card Specific Styles */\n.mantra-card {\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.03), rgba(244, 208, 63, 0.02));\n}\n\n.mantra-section {\n  text-align: center;\n}\n\n.mantra-subtitle {\n  font-size: 1.3rem;\n  color: #f4d03f;\n  margin: 2rem 0 1rem 0;\n  font-weight: 500;\n}\n\n.sanskrit-mantra {\n  font-size: 1.4rem;\n  color: #ffd700;\n  background: rgba(255, 215, 0, 0.1);\n  padding: 1.5rem;\n  border-radius: 10px;\n  margin: 1rem 0;\n  border: 1px solid rgba(255, 215, 0, 0.2);\n  font-family: 'Noto Sans Devanagari', serif;\n  line-height: 1.6;\n  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);\n}\n\n.sinhala-pronunciation {\n  font-size: 1.3rem;\n  color: #e8f4fd;\n  background: rgba(232, 244, 253, 0.1);\n  padding: 1.5rem;\n  border-radius: 10px;\n  margin: 1rem 0;\n  border: 1px solid rgba(232, 244, 253, 0.2);\n  line-height: 1.6;\n}\n\n.mantra-meaning {\n  font-size: 1.1rem;\n  color: #d5dbdb;\n  background: rgba(213, 219, 219, 0.1);\n  padding: 1.5rem;\n  border-radius: 10px;\n  margin: 1rem 0;\n  border: 1px solid rgba(213, 219, 219, 0.2);\n  text-align: justify;\n  line-height: 1.7;\n}\n\n/* Usage Guidelines Styles */\n.usage-guidelines {\n  display: flex;\n  flex-direction: column;\n  gap: 2rem;\n}\n\n.guideline-item {\n  background: rgba(255, 255, 255, 0.02);\n  padding: 1.5rem;\n  border-radius: 10px;\n  border-left: 4px solid #f4d03f;\n}\n\n.guideline-title {\n  font-size: 1.2rem;\n  color: #f4d03f;\n  margin-bottom: 0.8rem;\n  font-weight: 600;\n}\n\n.guideline-item p {\n  margin-bottom: 0;\n  color: #e8f4fd;\n}\n\n/* Benefits Card Styles */\n.benefits-card {\n  background: linear-gradient(135deg, rgba(46, 204, 113, 0.05), rgba(255, 255, 255, 0.02));\n}\n\n.benefits-list {\n  display: grid;\n  gap: 1.5rem;\n}\n\n.benefit-item {\n  display: flex;\n  align-items: flex-start;\n  gap: 1rem;\n  background: rgba(255, 255, 255, 0.02);\n  padding: 1.5rem;\n  border-radius: 10px;\n  border: 1px solid rgba(46, 204, 113, 0.2);\n  transition: all 0.3s ease;\n}\n\n.benefit-item:hover {\n  transform: translateY(-2px);\n  border-color: rgba(46, 204, 113, 0.4);\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);\n}\n\n.benefit-icon {\n  font-size: 2rem;\n  min-width: 3rem;\n  text-align: center;\n}\n\n.benefit-content h4 {\n  font-size: 1.2rem;\n  color: #2ecc71;\n  margin-bottom: 0.5rem;\n  font-weight: 600;\n}\n\n.benefit-content p {\n  margin-bottom: 0;\n  color: #e8f4fd;\n  line-height: 1.6;\n}\n\n/* Important Notes Card Styles */\n.important-notes-card {\n  background: linear-gradient(135deg, rgba(231, 76, 60, 0.05), rgba(255, 255, 255, 0.02));\n  border-color: rgba(231, 76, 60, 0.3);\n}\n\n.important-note {\n  background: rgba(231, 76, 60, 0.1);\n  padding: 2rem;\n  border-radius: 10px;\n  border: 1px solid rgba(231, 76, 60, 0.2);\n}\n\n.important-note p {\n  color: #ffeaa7;\n  font-weight: 500;\n  text-align: justify;\n  margin-bottom: 1.5rem;\n}\n\n.important-note p:last-child {\n  margin-bottom: 0;\n}\n\n/* Kubera Card Section Styles */\n.kubera-card-section {\n  width: 100%;\n  max-width: 1000px;\n  margin: 4rem auto;\n  padding: 0 2rem;\n  z-index: 2;\n  position: relative;\n}\n\n.kubera-card-container {\n  position: relative;\n  overflow: hidden;\n  margin-bottom: 3rem;\n}\n\n.product-header {\n  text-align: center;\n  margin-bottom: 2rem;\n}\n\n.product-title {\n  font-size: 2.5rem;\n  color: #f4d03f;\n  margin-bottom: 1rem;\n  text-shadow:\n    0 0 20px rgba(244, 208, 63, 0.6),\n    0 0 40px rgba(244, 208, 63, 0.4);\n  animation: divineGlow 3s ease-in-out infinite alternate;\n}\n\n.product-subtitle {\n  font-size: 1.3rem;\n  color: #e8f4fd;\n  line-height: 1.6;\n  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);\n}\n\n/* Product Slider Styles */\n.product-slider {\n  margin: 2rem 0;\n}\n\n.slider-container {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 1rem;\n}\n\n.image-container {\n  position: relative;\n  width: 400px;\n  height: 300px;\n  border-radius: 15px;\n  overflow: hidden;\n  border: 2px solid rgba(244, 208, 63, 0.3);\n  box-shadow:\n    0 10px 30px rgba(0, 0, 0, 0.3),\n    0 0 20px rgba(244, 208, 63, 0.1);\n}\n\n.product-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  transition: transform 0.3s ease;\n}\n\n.product-image:hover {\n  transform: scale(1.05);\n}\n\n.image-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(\n    to bottom,\n    rgba(0, 0, 0, 0.1) 0%,\n    transparent 30%,\n    transparent 70%,\n    rgba(0, 0, 0, 0.3) 100%\n  );\n  display: flex;\n  align-items: flex-end;\n  justify-content: flex-end;\n  padding: 1rem;\n}\n\n.overlay-content {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n}\n\n.image-counter {\n  background: rgba(0, 0, 0, 0.7);\n  color: #f4d03f;\n  padding: 0.3rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: 500;\n  border: 1px solid rgba(244, 208, 63, 0.3);\n}\n\n.slider-btn {\n  background: rgba(244, 208, 63, 0.2);\n  border: 1px solid rgba(244, 208, 63, 0.4);\n  color: #f4d03f;\n  width: 50px;\n  height: 50px;\n  border-radius: 50%;\n  font-size: 1.5rem;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  backdrop-filter: blur(10px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin: 0 1rem;\n}\n\n.slider-btn:hover {\n  background: rgba(244, 208, 63, 0.3);\n  border-color: rgba(244, 208, 63, 0.6);\n  transform: scale(1.1);\n  box-shadow: 0 0 15px rgba(244, 208, 63, 0.3);\n}\n\n.slider-dots {\n  display: flex;\n  justify-content: center;\n  gap: 0.5rem;\n  margin-top: 1rem;\n}\n\n.dot {\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  border: 1px solid rgba(244, 208, 63, 0.4);\n  background: rgba(244, 208, 63, 0.2);\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.dot.active {\n  background: rgba(244, 208, 63, 0.8);\n  border-color: rgba(244, 208, 63, 1);\n  box-shadow: 0 0 10px rgba(244, 208, 63, 0.5);\n}\n\n.dot:hover {\n  background: rgba(244, 208, 63, 0.6);\n  transform: scale(1.2);\n}\n\n/* Product Details Styles */\n.product-details {\n  margin: 2rem 0;\n}\n\n.product-features {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 1rem;\n  margin-bottom: 2rem;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  gap: 0.8rem;\n  padding: 1rem;\n  background: rgba(255, 255, 255, 0.03);\n  border: 1px solid rgba(244, 208, 63, 0.2);\n  border-radius: 10px;\n  transition: all 0.3s ease;\n}\n\n.feature-item:hover {\n  background: rgba(255, 255, 255, 0.05);\n  border-color: rgba(244, 208, 63, 0.4);\n  transform: translateY(-2px);\n}\n\n.feature-icon {\n  font-size: 1.2rem;\n  color: #f4d03f;\n}\n\n.feature-text {\n  color: #e8f4fd;\n  font-size: 0.95rem;\n  font-weight: 500;\n}\n\n/* Product Pricing Styles */\n.product-pricing {\n  text-align: center;\n  margin: 2rem 0;\n}\n\n.price-section {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: 1rem;\n  margin-bottom: 0.5rem;\n}\n\n.original-price {\n  font-size: 1.2rem;\n  color: #999;\n  text-decoration: line-through;\n}\n\n.current-price {\n  font-size: 2rem;\n  color: #f4d03f;\n  font-weight: 700;\n  text-shadow: 0 0 10px rgba(244, 208, 63, 0.3);\n}\n\n.discount-badge {\n  background: linear-gradient(135deg, #e74c3c, #c0392b);\n  color: white;\n  padding: 0.3rem 0.8rem;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  animation: pulse 2s infinite;\n}\n\n.price-note {\n  color: #a8d8ea;\n  font-size: 0.9rem;\n  font-style: italic;\n}\n\n/* Buy Now Section Styles */\n.buy-now-section {\n  text-align: center;\n  margin-top: 2rem;\n}\n\n.buy-now-btn {\n  background: linear-gradient(135deg, #f4d03f, #f1c40f);\n  border: none;\n  color: #1a1a2e;\n  padding: 1.2rem 2.5rem;\n  border-radius: 50px;\n  font-size: 1.3rem;\n  font-weight: 700;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: inline-flex;\n  align-items: center;\n  gap: 1rem;\n  box-shadow:\n    0 8px 25px rgba(244, 208, 63, 0.3),\n    0 0 20px rgba(244, 208, 63, 0.2);\n  margin-bottom: 1.5rem;\n}\n\n.buy-now-btn:hover {\n  transform: translateY(-3px) scale(1.05);\n  box-shadow:\n    0 12px 35px rgba(244, 208, 63, 0.4),\n    0 0 30px rgba(244, 208, 63, 0.3);\n  background: linear-gradient(135deg, #f7dc6f, #f4d03f);\n}\n\n.btn-icon {\n  font-size: 1.2rem;\n}\n\n.btn-text {\n  font-family: 'Noto Sans Sinhala', sans-serif;\n}\n\n.btn-arrow {\n  font-size: 1.1rem;\n  transition: transform 0.3s ease;\n}\n\n.buy-now-btn:hover .btn-arrow {\n  transform: translateX(5px);\n}\n\n.payment-info {\n  display: flex;\n  justify-content: center;\n  gap: 2rem;\n  flex-wrap: wrap;\n}\n\n.payment-method,\n.delivery-info {\n  display: flex;\n  align-items: center;\n  gap: 0.5rem;\n  color: #a8d8ea;\n  font-size: 0.95rem;\n}\n\n.payment-icon,\n.delivery-icon {\n  font-size: 1.1rem;\n  color: #f4d03f;\n}\n\n/* Checkout Overlay and Container Styles */\n.checkout-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.8);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n  padding: 1rem;\n  backdrop-filter: blur(5px);\n}\n\n.checkout-container {\n  width: 100%;\n  max-width: 600px;\n  max-height: 90vh;\n  overflow-y: auto;\n  position: relative;\n}\n\n.checkout-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 2rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid rgba(244, 208, 63, 0.3);\n}\n\n.checkout-header h2 {\n  color: #f4d03f;\n  font-size: 1.8rem;\n  margin: 0;\n}\n\n.close-btn {\n  background: none;\n  border: none;\n  color: #f4d03f;\n  font-size: 2rem;\n  cursor: pointer;\n  padding: 0;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 50%;\n  transition: all 0.3s ease;\n}\n\n.close-btn:hover {\n  background: rgba(244, 208, 63, 0.2);\n  transform: scale(1.1);\n}\n\n/* Progress Steps */\n.progress-steps {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 2rem;\n  position: relative;\n}\n\n.progress-steps::before {\n  content: '';\n  position: absolute;\n  top: 20px;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: rgba(244, 208, 63, 0.2);\n  z-index: 1;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  position: relative;\n  z-index: 2;\n}\n\n.step-number {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.1);\n  border: 2px solid rgba(244, 208, 63, 0.3);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #999;\n  font-weight: 600;\n  margin-bottom: 0.5rem;\n  transition: all 0.3s ease;\n}\n\n.step.active .step-number {\n  background: rgba(244, 208, 63, 0.2);\n  border-color: rgba(244, 208, 63, 0.8);\n  color: #f4d03f;\n}\n\n.step.completed .step-number {\n  background: rgba(244, 208, 63, 0.8);\n  border-color: rgba(244, 208, 63, 1);\n  color: #1a1a2e;\n}\n\n.step-label {\n  font-size: 0.9rem;\n  color: #999;\n  transition: color 0.3s ease;\n}\n\n.step.active .step-label {\n  color: #f4d03f;\n}\n\n.step.completed .step-label {\n  color: #f4d03f;\n}\n\n/* Checkout Content and Form Styles */\n.checkout-content {\n  margin-top: 1rem;\n}\n\n.checkout-step {\n  animation: fadeIn 0.3s ease-in-out;\n}\n\n.step-title {\n  color: #f4d03f;\n  font-size: 1.5rem;\n  margin-bottom: 1.5rem;\n  text-align: center;\n}\n\n.form-group {\n  margin-bottom: 1.5rem;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 1rem;\n}\n\n.form-group label {\n  display: block;\n  color: #e8f4fd;\n  font-size: 0.95rem;\n  margin-bottom: 0.5rem;\n  font-weight: 500;\n}\n\n.form-group input,\n.form-group textarea,\n.form-group select {\n  width: 100%;\n  padding: 0.8rem 1rem;\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  border-radius: 8px;\n  color: #e8f4fd;\n  font-size: 0.95rem;\n  transition: all 0.3s ease;\n  font-family: 'Noto Sans Sinhala', sans-serif;\n}\n\n.form-group input:focus,\n.form-group textarea:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: rgba(244, 208, 63, 0.6);\n  background: rgba(255, 255, 255, 0.08);\n  box-shadow: 0 0 10px rgba(244, 208, 63, 0.2);\n}\n\n.form-group input::placeholder,\n.form-group textarea::placeholder {\n  color: #999;\n}\n\n.form-group select {\n  cursor: pointer;\n}\n\n.form-group select option {\n  background: #1a1a2e;\n  color: #e8f4fd;\n  padding: 0.5rem;\n}\n\n.step-actions {\n  display: flex;\n  justify-content: space-between;\n  gap: 1rem;\n  margin-top: 2rem;\n  padding-top: 1.5rem;\n  border-top: 1px solid rgba(244, 208, 63, 0.2);\n}\n\n.btn-primary,\n.btn-secondary {\n  padding: 0.8rem 1.5rem;\n  border-radius: 8px;\n  font-size: 0.95rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  border: none;\n  font-family: 'Noto Sans Sinhala', sans-serif;\n}\n\n.btn-primary {\n  background: linear-gradient(135deg, #f4d03f, #f1c40f);\n  color: #1a1a2e;\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: linear-gradient(135deg, #f7dc6f, #f4d03f);\n  transform: translateY(-2px);\n  box-shadow: 0 5px 15px rgba(244, 208, 63, 0.3);\n}\n\n.btn-primary:disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n}\n\n.btn-secondary {\n  background: rgba(255, 255, 255, 0.1);\n  color: #e8f4fd;\n  border: 1px solid rgba(244, 208, 63, 0.3);\n}\n\n.btn-secondary:hover {\n  background: rgba(255, 255, 255, 0.15);\n  border-color: rgba(244, 208, 63, 0.5);\n}\n\n/* Order Summary Styles */\n.order-summary {\n  background: rgba(255, 255, 255, 0.03);\n  border: 1px solid rgba(244, 208, 63, 0.2);\n  border-radius: 10px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.product-summary {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  margin-bottom: 1.5rem;\n  padding-bottom: 1rem;\n  border-bottom: 1px solid rgba(244, 208, 63, 0.2);\n}\n\n.product-thumb {\n  width: 80px;\n  height: 60px;\n  object-fit: cover;\n  border-radius: 8px;\n  border: 1px solid rgba(244, 208, 63, 0.3);\n}\n\n.product-info h4 {\n  color: #f4d03f;\n  margin-bottom: 0.5rem;\n}\n\n.product-info p {\n  color: #e8f4fd;\n  font-size: 0.9rem;\n  margin-bottom: 0.5rem;\n}\n\n.price-info {\n  display: flex;\n  align-items: center;\n  gap: 0.8rem;\n}\n\n.price-info .original-price {\n  color: #999;\n  text-decoration: line-through;\n  font-size: 0.9rem;\n}\n\n.price-info .current-price {\n  color: #f4d03f;\n  font-weight: 600;\n  font-size: 1.1rem;\n}\n\n.order-total {\n  space-y: 0.5rem;\n}\n\n.total-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 0.5rem;\n  color: #e8f4fd;\n}\n\n.total-row .free {\n  color: #2ecc71;\n  font-weight: 600;\n}\n\n.final-total {\n  font-size: 1.2rem;\n  font-weight: 700;\n  color: #f4d03f;\n  padding-top: 0.5rem;\n  border-top: 1px solid rgba(244, 208, 63, 0.3);\n  margin-top: 0.5rem;\n}\n\n/* Customer and Payment Info */\n.customer-summary,\n.payment-method-info {\n  background: rgba(255, 255, 255, 0.03);\n  border: 1px solid rgba(244, 208, 63, 0.2);\n  border-radius: 10px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.customer-summary h4,\n.payment-method-info h4 {\n  color: #f4d03f;\n  margin-bottom: 1rem;\n}\n\n.address-info p {\n  color: #e8f4fd;\n  margin-bottom: 0.3rem;\n  line-height: 1.4;\n}\n\n.payment-option {\n  display: flex;\n  align-items: center;\n  gap: 1rem;\n  color: #e8f4fd;\n}\n\n.payment-note {\n  color: #a8d8ea;\n  font-size: 0.9rem;\n  font-style: italic;\n}\n\n/* Success Step Styles */\n.success-step {\n  text-align: center;\n}\n\n.success-icon {\n  font-size: 4rem;\n  margin-bottom: 1rem;\n  animation: bounce 1s ease-in-out;\n}\n\n.success-message {\n  color: #2ecc71;\n  font-size: 1.2rem;\n  font-weight: 600;\n  margin-bottom: 1.5rem;\n}\n\n.order-confirmation {\n  background: rgba(255, 255, 255, 0.03);\n  border: 1px solid rgba(46, 204, 113, 0.3);\n  border-radius: 10px;\n  padding: 1.5rem;\n  margin-bottom: 1.5rem;\n}\n\n.order-details {\n  margin-bottom: 1.5rem;\n}\n\n.order-details p {\n  color: #e8f4fd;\n  margin-bottom: 0.5rem;\n}\n\n.next-steps h4 {\n  color: #f4d03f;\n  margin-bottom: 1rem;\n}\n\n.next-steps ul {\n  text-align: left;\n  color: #e8f4fd;\n  line-height: 1.6;\n}\n\n.next-steps li {\n  margin-bottom: 0.5rem;\n}\n\n/* Animations */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes bounce {\n  0%, 20%, 50%, 80%, 100% {\n    transform: translateY(0);\n  }\n  40% {\n    transform: translateY(-10px);\n  }\n  60% {\n    transform: translateY(-5px);\n  }\n}\n\n/* Footer Styles */\n.kubera-footer {\n  margin-top: 4rem;\n  text-align: center;\n  padding: 2rem 0;\n}\n\n.kubera-footer .divine-blessing {\n  background: rgba(255, 255, 255, 0.05);\n  padding: 1.5rem 2rem;\n  border-radius: 15px;\n  border: 1px solid rgba(244, 208, 63, 0.3);\n  display: inline-block;\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n.floating {\n  animation: floating 6s ease-in-out infinite;\n}\n\n@keyframes floating {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-10px);\n  }\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  }\n  50% {\n    transform: scale(1.05);\n    box-shadow: 0 12px 40px rgba(244, 208, 63, 0.4);\n  }\n  100% {\n    transform: scale(1);\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n  }\n}\n\n/* Responsive Design for Kubera Guide */\n@media (max-width: 768px) {\n  .kubera-guide-page {\n    padding: 1rem 0.5rem;\n  }\n\n  /* Kubera Card Section Mobile Styles */\n  .kubera-card-section {\n    padding: 0 1rem;\n    margin: 2rem auto;\n  }\n\n  .product-title {\n    font-size: 2rem;\n  }\n\n  .product-subtitle {\n    font-size: 1.1rem;\n  }\n\n  .image-container {\n    width: 300px;\n    height: 225px;\n  }\n\n  .slider-btn {\n    width: 40px;\n    height: 40px;\n    font-size: 1.2rem;\n    margin: 0 0.5rem;\n  }\n\n  .product-features {\n    grid-template-columns: 1fr;\n    gap: 0.8rem;\n  }\n\n  .feature-item {\n    padding: 0.8rem;\n  }\n\n  .current-price {\n    font-size: 1.6rem;\n  }\n\n  .buy-now-btn {\n    padding: 1rem 2rem;\n    font-size: 1.1rem;\n  }\n\n  .payment-info {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .content-title {\n    font-size: 1.6rem;\n  }\n\n  .content-body {\n    font-size: 1rem;\n  }\n\n  .sanskrit-mantra {\n    font-size: 1.2rem;\n    padding: 1rem;\n  }\n\n  .sinhala-pronunciation {\n    font-size: 1.1rem;\n    padding: 1rem;\n  }\n\n  .mantra-meaning {\n    font-size: 1rem;\n    padding: 1rem;\n  }\n\n  .benefit-item {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .benefit-icon {\n    min-width: auto;\n  }\n\n  .zodiac-nav-btn {\n    padding: 0.8rem 1.5rem;\n    font-size: 1rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .main-title {\n    font-size: 2.5rem;\n  }\n\n  .subtitle {\n    font-size: 1.4rem;\n  }\n\n  /* Kubera Card Section Small Mobile Styles */\n  .kubera-card-section {\n    padding: 0 0.5rem;\n    margin: 1.5rem auto;\n  }\n\n  .product-title {\n    font-size: 1.8rem;\n  }\n\n  .product-subtitle {\n    font-size: 1rem;\n  }\n\n  .image-container {\n    width: 250px;\n    height: 188px;\n  }\n\n  .slider-btn {\n    width: 35px;\n    height: 35px;\n    font-size: 1rem;\n    margin: 0 0.3rem;\n  }\n\n  .current-price {\n    font-size: 1.4rem;\n  }\n\n  .buy-now-btn {\n    padding: 0.9rem 1.5rem;\n    font-size: 1rem;\n  }\n\n  .feature-text {\n    font-size: 0.85rem;\n  }\n\n  /* Checkout Mobile Styles */\n  .checkout-container {\n    margin: 0.5rem;\n    max-height: 95vh;\n  }\n\n  .checkout-header h2 {\n    font-size: 1.4rem;\n  }\n\n  .progress-steps {\n    margin-bottom: 1.5rem;\n  }\n\n  .step-number {\n    width: 35px;\n    height: 35px;\n    font-size: 0.9rem;\n  }\n\n  .step-label {\n    font-size: 0.8rem;\n  }\n\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n\n  .step-actions {\n    flex-direction: column;\n    gap: 0.8rem;\n  }\n\n  .btn-primary,\n  .btn-secondary {\n    width: 100%;\n    padding: 1rem;\n  }\n\n  .product-summary {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .payment-info {\n    flex-direction: column;\n    gap: 1rem;\n  }\n\n  .content-title {\n    font-size: 1.4rem;\n  }\n\n  .kubera-content-card {\n    padding: 1.5rem;\n  }\n\n  .guideline-item {\n    padding: 1rem;\n  }\n\n  .benefit-item {\n    padding: 1rem;\n  }\n\n  .important-note {\n    padding: 1.5rem;\n  }\n}"], "names": [], "sourceRoot": ""}