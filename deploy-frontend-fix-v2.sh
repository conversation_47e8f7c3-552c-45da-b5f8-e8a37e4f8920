#!/bin/bash

# Deploy Frontend Fix v2 - <PERSON>le permissions correctly
# This script rebuilds and deploys the frontend with correct API configuration

set -e

# Configuration
SERVER_IP="************"
SERVER_USER="ubuntu"
SSH_KEY="/tmp/kubera_deploy.pem"
REMOTE_PATH="/var/www/kubera.help"
DOMAIN="kubera.help"

echo "🚀 Deploying Frontend Fix v2..."
echo "================================"

# Ensure SSH key exists and has correct permissions
if [ ! -f "$SSH_KEY" ]; then
    echo "❌ SSH key not found at $SSH_KEY"
    echo "Copying SSH key from kubera.pem..."
    cp kubera.pem "$SSH_KEY"
    chmod 600 "$SSH_KEY"
fi

# Test SSH connection
echo "🔍 Testing SSH connection..."
ssh -i "$SSH_KEY" -o ConnectTimeout=10 "$SERVER_USER@$SERVER_IP" "echo 'SSH connection successful'"

# Step 1: Remove old build and create fresh one
echo "💾 Removing old build and creating backup on server..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "cd $REMOTE_PATH && sudo rm -rf build_old && sudo mv build build_old 2>/dev/null || true"

# Step 2: Upload new build
echo "📤 Uploading new frontend build..."
echo "Compressing build directory..."
tar -czf build.tar.gz build/

echo "Uploading compressed build..."
scp -i "$SSH_KEY" build.tar.gz "$SERVER_USER@$SERVER_IP:$REMOTE_PATH/"

echo "Extracting and setting permissions on server..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" << 'EOF'
cd /var/www/kubera.help
tar -xzf build.tar.gz
sudo chown -R www-data:www-data build/
sudo chmod -R 755 build/
rm build.tar.gz
echo "✅ Build extracted and permissions set"
EOF

# Step 3: Test the deployment
echo "🧪 Testing deployment..."

echo "Testing static file access..."
STATIC_TEST=$(curl -s -o /dev/null -w "%{http_code}" https://$DOMAIN/)
echo "Static file response code: $STATIC_TEST"

if [ "$STATIC_TEST" = "200" ]; then
    echo "✅ Static files are serving correctly"
else
    echo "❌ Static files not serving correctly (HTTP $STATIC_TEST)"
fi

echo "Testing API health..."
API_HEALTH=$(curl -s https://$DOMAIN/api/health)
echo "API health response: $API_HEALTH"

echo "Testing horoscope API..."
HOROSCOPE_TEST=$(curl -s "https://$DOMAIN/api/horoscope/taurus" | jq -r '.success // "API_ERROR"' 2>/dev/null || echo "API_ERROR")
echo "Horoscope API test result: $HOROSCOPE_TEST"

# Step 4: Check and reload Nginx
echo "🔧 Checking and reloading Nginx..."
ssh -i "$SSH_KEY" "$SERVER_USER@$SERVER_IP" "sudo nginx -t && sudo systemctl reload nginx"

# Clean up local files
echo "🧹 Cleaning up..."
rm -f build.tar.gz

echo ""
echo "🎉 Frontend deployment completed!"
echo "✅ New build deployed with correct API configuration"
echo "🔗 Website URL: https://$DOMAIN"
echo ""
echo "📊 Test Results:"
echo "- Static files: HTTP $STATIC_TEST"
echo "- API health: $API_HEALTH"
echo "- Horoscope API: $HOROSCOPE_TEST"
echo ""
echo "🔍 Next steps:"
echo "1. Open https://$DOMAIN in your browser"
echo "2. Check if horoscope content loads properly"
echo "3. Test order placement functionality"
echo "4. Check browser console for any errors"
